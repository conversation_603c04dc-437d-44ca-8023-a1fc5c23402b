-- ============================================================================
-- FREELANCIFY - NUCLEAR DATABASE CLEANUP
-- ============================================================================
-- This script COMPLETELY DESTROYS everything in the public schema and starts fresh.
-- This is a nuclear option that removes ALL tables, functions, policies, etc.
--
-- ⚠️  EXTREME WARNING: This will permanently delete EVERYTHING in your database!
-- ⚠️  This includes ALL tables, ALL data, ALL functions, ALL policies, etc.
-- ⚠️  Make sure you have a backup if you need to preserve ANY data.
--
-- Instructions:
-- 1. Connect to your Supabase database
-- 2. Execute this script to destroy everything
-- 3. Then apply: supabase/migrations/001_comprehensive_schema.sql
-- ============================================================================

-- ============================================================================
-- STEP 1: DROP ALL POLICIES (RLS and Storage)
-- ============================================================================

-- Drop all RLS policies from all tables
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON ' || r.schemaname || '.' || r.tablename;
    END LOOP;
END $$;

-- Drop all storage policies
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT policyname FROM pg_policies WHERE schemaname = 'storage') LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON storage.objects';
    END LOOP;
END $$;

-- ============================================================================
-- STEP 2: DISABLE RLS ON ALL TABLES
-- ============================================================================

DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'ALTER TABLE IF EXISTS public.' || r.tablename || ' DISABLE ROW LEVEL SECURITY';
    END LOOP;
END $$;

-- ============================================================================
-- STEP 3: DROP ALL TRIGGERS
-- ============================================================================

DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT trigger_name, event_object_table FROM information_schema.triggers WHERE trigger_schema = 'public') LOOP
        EXECUTE 'DROP TRIGGER IF EXISTS ' || r.trigger_name || ' ON public.' || r.event_object_table;
    END LOOP;
END $$;

-- ============================================================================
-- STEP 4: DROP ALL FUNCTIONS
-- ============================================================================

DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT proname, oidvectortypes(proargtypes) as argtypes FROM pg_proc INNER JOIN pg_namespace ON pg_proc.pronamespace = pg_namespace.oid WHERE pg_namespace.nspname = 'public') LOOP
        EXECUTE 'DROP FUNCTION IF EXISTS public.' || r.proname || '(' || r.argtypes || ') CASCADE';
    END LOOP;
END $$;

-- ============================================================================
-- STEP 5: DROP ALL VIEWS
-- ============================================================================

DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT viewname FROM pg_views WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP VIEW IF EXISTS public.' || r.viewname || ' CASCADE';
    END LOOP;
END $$;

-- ============================================================================
-- STEP 6: DROP ALL TABLES
-- ============================================================================

DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP TABLE IF EXISTS public.' || r.tablename || ' CASCADE';
    END LOOP;
END $$;

-- ============================================================================
-- STEP 7: DROP ALL SEQUENCES
-- ============================================================================

DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT sequencename FROM pg_sequences WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP SEQUENCE IF EXISTS public.' || r.sequencename || ' CASCADE';
    END LOOP;
END $$;

-- ============================================================================
-- STEP 8: DROP ALL TYPES
-- ============================================================================

DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT typname FROM pg_type WHERE typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public') AND typtype = 'e') LOOP
        EXECUTE 'DROP TYPE IF EXISTS public.' || r.typname || ' CASCADE';
    END LOOP;
END $$;

-- ============================================================================
-- STEP 9: CLEAN UP STORAGE
-- ============================================================================

-- Remove all storage buckets
DELETE FROM storage.buckets;

-- Remove all storage objects
DELETE FROM storage.objects;

-- ============================================================================
-- STEP 10: RESET EXTENSIONS (if needed)
-- ============================================================================

-- Drop and recreate uuid-ossp extension to reset it
DROP EXTENSION IF EXISTS "uuid-ossp" CASCADE;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- VERIFICATION AND COMPLETION
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'NUCLEAR DATABASE CLEANUP COMPLETED';
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'EVERYTHING has been destroyed and removed:';
  RAISE NOTICE '- All tables and their data';
  RAISE NOTICE '- All functions and procedures';
  RAISE NOTICE '- All triggers';
  RAISE NOTICE '- All views';
  RAISE NOTICE '- All sequences';
  RAISE NOTICE '- All custom types';
  RAISE NOTICE '- All RLS policies';
  RAISE NOTICE '- All storage buckets and objects';
  RAISE NOTICE '';
  RAISE NOTICE 'The public schema is now completely empty and ready for fresh setup.';
  RAISE NOTICE '';
  RAISE NOTICE 'Next step: Apply the comprehensive schema';
  RAISE NOTICE 'Command: psql -d your_database -f supabase/migrations/001_comprehensive_schema.sql';
  RAISE NOTICE '============================================================================';
END $$;
