-- ============================================================================
-- FREELANCIFY - COMPLETE DATABASE CLEANUP
-- ============================================================================
-- This script completely removes all existing data and tables to start fresh.
-- Execute this BEFORE applying the new comprehensive schema.
--
-- ⚠️  WARNING: This will permanently delete ALL data in your database!
-- ⚠️  Make sure you have a backup if you need to preserve any data.
--
-- Instructions:
-- 1. Connect to your Supabase database
-- 2. Execute this script to clean everything
-- 3. Then apply: supabase/migrations/001_comprehensive_schema.sql
-- ============================================================================

-- Disable RLS temporarily for cleanup
ALTER TABLE IF EXISTS public.freelance_projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.user_settings DISABLE ROW LEVEL SECURITY;

-- Drop all RLS policies
DROP POLICY IF EXISTS "freelance_projects_select_policy" ON public.freelance_projects;
DROP POLICY IF EXISTS "freelance_projects_insert_policy" ON public.freelance_projects;
DROP POLICY IF EXISTS "freelance_projects_update_policy" ON public.freelance_projects;
DROP POLICY IF EXISTS "freelance_projects_delete_policy" ON public.freelance_projects;
DROP POLICY IF EXISTS "user_settings_select_policy" ON public.user_settings;
DROP POLICY IF EXISTS "user_settings_insert_policy" ON public.user_settings;
DROP POLICY IF EXISTS "user_settings_update_policy" ON public.user_settings;

-- Drop all indexes
DROP INDEX IF EXISTS idx_freelance_projects_org_id;
DROP INDEX IF EXISTS idx_freelance_projects_org_status;
DROP INDEX IF EXISTS idx_freelance_projects_org_user;
DROP INDEX IF EXISTS idx_freelance_projects_user_id;
DROP INDEX IF EXISTS idx_freelance_projects_status;
DROP INDEX IF EXISTS idx_freelance_projects_created_at;
DROP INDEX IF EXISTS idx_freelance_projects_deadline;
DROP INDEX IF EXISTS idx_freelance_projects_user_status;
DROP INDEX IF EXISTS idx_user_settings_user_id;

-- Drop all functions
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop all triggers
DROP TRIGGER IF EXISTS update_user_settings_updated_at ON public.user_settings;
DROP TRIGGER IF EXISTS update_freelance_projects_updated_at ON public.freelance_projects;

-- Drop all tables (in correct order to avoid foreign key constraints)
DROP TABLE IF EXISTS public.freelance_projects CASCADE;
DROP TABLE IF EXISTS public.user_settings CASCADE;

-- Drop storage buckets if they exist
DELETE FROM storage.buckets WHERE id = 'cv-uploads';

-- Verification queries
DO $$
BEGIN
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'DATABASE CLEANUP COMPLETED';
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'All tables, policies, indexes, functions, and triggers have been removed.';
  RAISE NOTICE '';
  RAISE NOTICE 'Next step: Apply the comprehensive schema';
  RAISE NOTICE 'Command: psql -d your_database -f supabase/migrations/001_comprehensive_schema.sql';
  RAISE NOTICE '============================================================================';
END $$;
