-- ============================================================================
-- FREELANCIFY - COMPREHENSIVE USER-BASED DATABASE SCHEMA
-- ============================================================================
-- This file contains the complete database schema for the Freelancify application
-- with simplified user-based architecture and direct user-to-project relationships.
--
-- Features:
-- - Direct user-based data ownership (no organizations)
-- - Row Level Security (RLS) for user data isolation
-- - User authentication and authorization
-- - Comprehensive user settings for application generation
-- - Project management with user scoping
-- - CV upload and storage support
-- - Clean, maintainable architecture
--
-- Migration: 001_comprehensive_schema.sql
-- Date: 2025-01-13
-- Purpose: Complete database setup from scratch with user-based architecture
-- ============================================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- STORAGE SETUP
-- ============================================================================

-- Create storage bucket for CV uploads
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'cv-uploads',
  'cv-uploads',
  true,
  5242880, -- 5MB limit
  ARRAY['application/pdf']
) ON CONFLICT (id) DO NOTHING;

-- Set up storage policies for CV uploads
CREATE POLICY "Users can upload their own CVs"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'cv-uploads' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can view their own CVs"
ON storage.objects FOR SELECT
USING (
  bucket_id = 'cv-uploads' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can update their own CVs"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'cv-uploads' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own CVs"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'cv-uploads' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- ============================================================================
-- USER SETTINGS TABLE
-- ============================================================================

-- User settings table - Comprehensive user profile and preferences
-- This table stores all user data needed for application generation and profile management
CREATE TABLE IF NOT EXISTS public.user_settings (
  -- Core identifiers
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,

  -- Profile information (used in application generation)
  full_name TEXT,
  professional_email TEXT,
  phone TEXT,
  address TEXT,
  website TEXT,

  -- Professional information
  hourly_rate_eur INTEGER CHECK (hourly_rate_eur > 0 AND hourly_rate_eur <= 10000),

  -- Availability information (used in application generation)
  availability_start_date DATE,
  availability_end_date DATE,
  availability_hours_per_week INTEGER CHECK (availability_hours_per_week > 0 AND availability_hours_per_week <= 168),
  availability_notes TEXT,

  -- CV and document storage
  cv_pdf_url TEXT,

  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

  -- Data validation constraints
  CONSTRAINT valid_availability_date_range CHECK (
    availability_end_date IS NULL OR
    availability_start_date IS NULL OR
    availability_end_date >= availability_start_date
  ),
  CONSTRAINT valid_email_format CHECK (
    professional_email IS NULL OR
    professional_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
  ),
  CONSTRAINT valid_website_format CHECK (
    website IS NULL OR
    website ~* '^https?://[^\s/$.?#].[^\s]*$'
  )
);

-- User settings indexes
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON public.user_settings(user_id);

-- ============================================================================
-- FREELANCE PROJECTS TABLE
-- ============================================================================

-- Freelance projects table - Core business entity with direct user ownership
-- Each project belongs directly to a user for simple data ownership
CREATE TABLE IF NOT EXISTS public.freelance_projects (
  -- Core identifiers
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Project basic information (required fields)
  project_name TEXT NOT NULL CHECK (length(trim(project_name)) > 0),
  company_name TEXT NOT NULL CHECK (length(trim(company_name)) > 0),

  -- Contact information (optional)
  contact_person TEXT,
  contact_email TEXT,
  contact_phone TEXT,

  -- Project details (optional)
  project_description TEXT,
  budget_range TEXT,
  project_start_date DATE,
  project_end_date DATE,
  required_skills TEXT[],

  -- Application tracking
  application_date DATE,
  status TEXT NOT NULL CHECK (status IN (
    'not_applied', 'application_sent', 'inquiry_received',
    'interview_scheduled', 'interview_completed', 'offer_received',
    'rejected', 'project_completed'
  )) DEFAULT 'not_applied',
  application_text TEXT,

  -- Additional information
  notes TEXT,
  source TEXT,
  listing_url TEXT,

  -- Work location details
  work_location_type TEXT CHECK (work_location_type IN ('remote', 'onsite', 'hybrid', 'flexible')),
  remote_percentage INTEGER CHECK (remote_percentage >= 0 AND remote_percentage <= 100),
  work_location_notes TEXT,

  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

  -- Data validation constraints
  CONSTRAINT valid_project_date_range CHECK (
    project_end_date IS NULL OR
    project_start_date IS NULL OR
    project_end_date >= project_start_date
  ),
  CONSTRAINT valid_contact_email_format CHECK (
    contact_email IS NULL OR
    contact_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
  )
);

-- Freelance projects indexes for performance
CREATE INDEX IF NOT EXISTS idx_freelance_projects_user_id ON public.freelance_projects(user_id);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_status ON public.freelance_projects(status);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_created_at ON public.freelance_projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_application_date ON public.freelance_projects(application_date) WHERE application_date IS NOT NULL;

-- Composite index for common user-based queries
CREATE INDEX IF NOT EXISTS idx_freelance_projects_user_status ON public.freelance_projects(user_id, status);

-- ============================================================================
-- AUTOMATIC TIMESTAMP UPDATE FUNCTION
-- ============================================================================

-- Function to automatically update updated_at timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- ============================================================================

-- Trigger for user_settings table
CREATE TRIGGER update_user_settings_updated_at
  BEFORE UPDATE ON public.user_settings
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Trigger for freelance_projects table
CREATE TRIGGER update_freelance_projects_updated_at
  BEFORE UPDATE ON public.freelance_projects
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- ============================================================================
-- ROW LEVEL SECURITY (RLS) SETUP
-- ============================================================================

-- ============================================================================
-- TABLE PERMISSIONS
-- ============================================================================

-- Grant necessary permissions to Supabase roles
GRANT ALL ON public.user_settings TO authenticated;
GRANT ALL ON public.user_settings TO anon;
GRANT ALL ON public.freelance_projects TO authenticated;
GRANT ALL ON public.freelance_projects TO anon;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon;

-- Enable RLS on all tables
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.freelance_projects ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- RLS POLICIES - USER SETTINGS
-- ============================================================================

-- Users can view their own settings
CREATE POLICY "user_settings_select_policy"
ON public.user_settings FOR SELECT
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can create their own settings
CREATE POLICY "user_settings_insert_policy"
ON public.user_settings FOR INSERT
WITH CHECK (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can update their own settings
CREATE POLICY "user_settings_update_policy"
ON public.user_settings FOR UPDATE
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can delete their own settings
CREATE POLICY "user_settings_delete_policy"
ON public.user_settings FOR DELETE
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- ============================================================================
-- RLS POLICIES - FREELANCE PROJECTS
-- ============================================================================

-- Users can view their own projects
CREATE POLICY "freelance_projects_select_policy"
ON public.freelance_projects FOR SELECT
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can create their own projects
CREATE POLICY "freelance_projects_insert_policy"
ON public.freelance_projects FOR INSERT
WITH CHECK (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can update their own projects
CREATE POLICY "freelance_projects_update_policy"
ON public.freelance_projects FOR UPDATE
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can delete their own projects
CREATE POLICY "freelance_projects_delete_policy"
ON public.freelance_projects FOR DELETE
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'FREELANCIFY COMPREHENSIVE SCHEMA SETUP COMPLETE';
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'Tables created:';
  RAISE NOTICE '- user_settings (comprehensive user profile and preferences)';
  RAISE NOTICE '- freelance_projects (direct user ownership)';
  RAISE NOTICE '';
  RAISE NOTICE 'Storage setup:';
  RAISE NOTICE '- cv-uploads bucket with proper policies';
  RAISE NOTICE '';
  RAISE NOTICE 'Security features:';
  RAISE NOTICE '- Row Level Security (RLS) on all tables';
  RAISE NOTICE '- Direct user-based data isolation';
  RAISE NOTICE '- Comprehensive data validation constraints';
  RAISE NOTICE '- Automatic timestamp updates';
  RAISE NOTICE '';
  RAISE NOTICE 'User settings fields available for application generation:';
  RAISE NOTICE '- Profile: full_name, professional_email, phone, address, website';
  RAISE NOTICE '- Professional: hourly_rate_eur';
  RAISE NOTICE '- Availability: start_date, end_date, hours_per_week, notes';
  RAISE NOTICE '- Documents: cv_pdf_url';
  RAISE NOTICE '';
  RAISE NOTICE 'Ready for application use!';
  RAISE NOTICE '============================================================================';
END $$;
