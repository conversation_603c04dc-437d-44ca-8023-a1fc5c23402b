-- ============================================================================
-- FREELANCIFY - SIMPLIFIED USER-BASED DATABASE SCHEMA
-- ============================================================================
-- This file contains the simplified database schema for the Freelancify application
-- with direct user-to-project relationships, removing the organization layer entirely.
--
-- Features:
-- - Direct user-based data ownership (no organizations)
-- - Row Level Security (RLS) for user data isolation
-- - User authentication and authorization
-- - Simplified project management with user scoping
-- - Clean, non-over-engineered architecture
--
-- Migration: 002_user_based_schema.sql
-- Date: 2025-01-13
-- Purpose: Remove organization structure and implement direct user-project relationships
-- ============================================================================

-- ============================================================================
-- DROP ORGANIZATION-RELATED STRUCTURES
-- ============================================================================

-- Drop existing RLS policies for organization tables
DROP POLICY IF EXISTS "organizations_select_policy" ON public.organizations;
DROP POLICY IF EXISTS "organizations_insert_policy" ON public.organizations;
DROP POLICY IF EXISTS "organizations_update_policy" ON public.organizations;
DROP POLICY IF EXISTS "organizations_delete_policy" ON public.organizations;

DROP POLICY IF EXISTS "organization_members_select_policy" ON public.organization_members;
DROP POLICY IF EXISTS "organization_owners_view_members_policy" ON public.organization_members;
DROP POLICY IF EXISTS "organization_members_insert_policy" ON public.organization_members;
DROP POLICY IF EXISTS "organization_members_update_policy" ON public.organization_members;
DROP POLICY IF EXISTS "organization_members_delete_policy" ON public.organization_members;

-- Drop existing RLS policies for projects (will recreate with user-based logic)
DROP POLICY IF EXISTS "freelance_projects_select_policy" ON public.freelance_projects;
DROP POLICY IF EXISTS "freelance_projects_insert_policy" ON public.freelance_projects;
DROP POLICY IF EXISTS "freelance_projects_update_policy" ON public.freelance_projects;
DROP POLICY IF EXISTS "freelance_projects_delete_policy" ON public.freelance_projects;

-- Drop organization-related indexes
DROP INDEX IF EXISTS idx_organizations_owner_id;
DROP INDEX IF EXISTS idx_organizations_slug;
DROP INDEX IF EXISTS idx_organizations_created_at;
DROP INDEX IF EXISTS idx_organization_members_org_id;
DROP INDEX IF EXISTS idx_organization_members_user_id;
DROP INDEX IF EXISTS idx_organization_members_role;
DROP INDEX IF EXISTS idx_freelance_projects_org_id;
DROP INDEX IF EXISTS idx_freelance_projects_org_status;
DROP INDEX IF EXISTS idx_freelance_projects_org_user;

-- Drop organization-related helper functions
DROP FUNCTION IF EXISTS get_user_organizations(uuid);
DROP FUNCTION IF EXISTS is_organization_member(uuid, uuid);
DROP FUNCTION IF EXISTS is_organization_owner(uuid, uuid);

-- ============================================================================
-- UPDATE FREELANCE PROJECTS TABLE
-- ============================================================================

-- Remove organization_id column from freelance_projects
ALTER TABLE public.freelance_projects 
DROP COLUMN IF EXISTS organization_id;

-- Update table comment to reflect new structure
COMMENT ON TABLE public.freelance_projects IS 
'Freelance projects table - Core business entity. Each project belongs directly to a user for simple data ownership.';

-- ============================================================================
-- SIMPLIFIED USER-BASED INDEXES
-- ============================================================================

-- Keep essential indexes for performance
CREATE INDEX IF NOT EXISTS idx_freelance_projects_user_id ON public.freelance_projects(user_id);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_status ON public.freelance_projects(status);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_created_at ON public.freelance_projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_deadline ON public.freelance_projects(deadline) WHERE deadline IS NOT NULL;

-- Composite index for common user-based queries
CREATE INDEX IF NOT EXISTS idx_freelance_projects_user_status ON public.freelance_projects(user_id, status);

-- ============================================================================
-- SIMPLIFIED RLS POLICIES - USER-BASED ACCESS
-- ============================================================================

-- Users can view their own projects
CREATE POLICY "freelance_projects_select_policy"
ON public.freelance_projects FOR SELECT
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can create their own projects
CREATE POLICY "freelance_projects_insert_policy"
ON public.freelance_projects FOR INSERT
WITH CHECK (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can update their own projects
CREATE POLICY "freelance_projects_update_policy"
ON public.freelance_projects FOR UPDATE
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- Users can delete their own projects
CREATE POLICY "freelance_projects_delete_policy"
ON public.freelance_projects FOR DELETE
USING (
  auth.uid() IS NOT NULL
  AND user_id = auth.uid()
);

-- ============================================================================
-- USER SETTINGS RLS POLICIES (unchanged - already user-based)
-- ============================================================================

-- Ensure user settings policies exist (these should already be correct)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'user_settings'
    AND policyname = 'user_settings_select_policy'
  ) THEN
    CREATE POLICY "user_settings_select_policy"
    ON public.user_settings FOR SELECT
    USING (
      auth.uid() IS NOT NULL
      AND user_id = auth.uid()
    );
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'user_settings'
    AND policyname = 'user_settings_insert_policy'
  ) THEN
    CREATE POLICY "user_settings_insert_policy"
    ON public.user_settings FOR INSERT
    WITH CHECK (
      auth.uid() IS NOT NULL
      AND user_id = auth.uid()
    );
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'user_settings'
    AND policyname = 'user_settings_update_policy'
  ) THEN
    CREATE POLICY "user_settings_update_policy"
    ON public.user_settings FOR UPDATE
    USING (
      auth.uid() IS NOT NULL
      AND user_id = auth.uid()
    );
  END IF;
END $$;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'FREELANCIFY SIMPLIFIED SCHEMA MIGRATION COMPLETE';
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'Changes applied:';
  RAISE NOTICE '- Removed organization tables and related structures';
  RAISE NOTICE '- Updated freelance_projects for direct user ownership';
  RAISE NOTICE '- Implemented simplified user-based RLS policies';
  RAISE NOTICE '- Removed organization-related indexes and functions';
  RAISE NOTICE '';
  RAISE NOTICE 'Remaining tables:';
  RAISE NOTICE '- user_settings (user preferences and profile data)';
  RAISE NOTICE '- freelance_projects (direct user ownership)';
  RAISE NOTICE '';
  RAISE NOTICE 'Security features:';
  RAISE NOTICE '- Row Level Security (RLS) on all tables';
  RAISE NOTICE '- Direct user-based data isolation';
  RAISE NOTICE '- Simplified, non-over-engineered architecture';
  RAISE NOTICE '============================================================================';
END $$;
