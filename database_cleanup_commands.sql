-- ============================================================================
-- FREELANCIFY - DATABASE CLEANUP COMMANDS
-- ============================================================================
-- These commands will remove all existing organization-related data from your
-- current database. Execute these commands MANUALLY before applying the new
-- user-based schema migration.
--
-- ⚠️  WARNING: These commands will permanently delete all organization data!
-- ⚠️  Make sure you have a backup if you need to preserve any data.
--
-- Instructions:
-- 1. Connect to your Supabase database
-- 2. Execute these commands in order
-- 3. Then apply the new migration: 002_user_based_schema.sql
-- ============================================================================

-- ============================================================================
-- STEP 1: DISABLE RLS TEMPORARILY FOR CLEANUP
-- ============================================================================
-- This ensures we can delete all data regardless of current policies

ALTER TABLE public.organization_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.freelance_projects DISABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 2: DELETE ALL ORGANIZATION-RELATED DATA
-- ============================================================================
-- Delete in correct order to avoid foreign key constraint violations

-- Delete all organization memberships first
DELETE FROM public.organization_members;

-- Delete all organizations
DELETE FROM public.organizations;

-- Note: We don't delete freelance_projects here because they contain valuable
-- user data. The migration will remove the organization_id column instead.

-- ============================================================================
-- STEP 3: RE-ENABLE RLS (will be updated by migration)
-- ============================================================================

ALTER TABLE public.freelance_projects ENABLE ROW LEVEL SECURITY;

-- Note: We don't re-enable RLS for organization tables since they will be
-- dropped by the migration script.

-- ============================================================================
-- STEP 4: VERIFICATION QUERIES
-- ============================================================================
-- Run these to verify the cleanup was successful

-- Should return 0 rows
SELECT COUNT(*) as remaining_organizations FROM public.organizations;
SELECT COUNT(*) as remaining_memberships FROM public.organization_members;

-- Should show your projects (these will be preserved)
SELECT COUNT(*) as total_projects FROM public.freelance_projects;
SELECT COUNT(*) as projects_with_org_id FROM public.freelance_projects WHERE organization_id IS NOT NULL;

-- ============================================================================
-- STEP 5: NEXT STEPS
-- ============================================================================
-- After running these cleanup commands:
--
-- 1. Apply the new migration:
--    psql -d your_database -f supabase/migrations/002_user_based_schema.sql
--
-- 2. Verify the migration was successful:
--    - Check that organization tables no longer exist
--    - Check that freelance_projects no longer has organization_id column
--    - Check that new RLS policies are in place
--
-- 3. Update your application code to remove organization references
--
-- ============================================================================

-- Optional: Drop the organization tables entirely (if migration doesn't handle it)
-- Uncomment these lines if needed:

-- DROP TABLE IF EXISTS public.organization_members CASCADE;
-- DROP TABLE IF EXISTS public.organizations CASCADE;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'DATABASE CLEANUP COMPLETED';
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'Organization data has been removed from:';
  RAISE NOTICE '- organization_members table (all records deleted)';
  RAISE NOTICE '- organizations table (all records deleted)';
  RAISE NOTICE '';
  RAISE NOTICE 'Preserved data:';
  RAISE NOTICE '- freelance_projects (user data preserved)';
  RAISE NOTICE '- user_settings (user preferences preserved)';
  RAISE NOTICE '';
  RAISE NOTICE 'Next step: Apply the new migration file';
  RAISE NOTICE 'Command: psql -d your_database -f supabase/migrations/002_user_based_schema.sql';
  RAISE NOTICE '============================================================================';
END $$;
