import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useUserSettings } from '@/hooks/useUserSettings';
import { useFreelanceProjects } from '@/hooks/useFreelanceProjects';

import { useExport } from '@/hooks/useExport';
import { useToast } from '@/hooks/use-toast';
import { Download, Upload, Save, Calendar, Clock, User, Globe, Phone, Mail, MapPin, FileText, CheckCircle, FileSpreadsheet, FileType } from 'lucide-react';
import { ExportData } from '@/types/settings';
import { supabase } from '@/integrations/supabase/client';

export const Settings = () => {
  const { settings, saveSettings, isSaving } = useUserSettings();
  const { projects } = useFreelanceProjects();

  const { toast } = useToast();
  const { exportToPDF, exportToExcel, exportToJSON, isExporting, progress } = useExport({
    projects: projects || [],
    settings
  });

  // Profile data state
  const [fullName, setFullName] = useState('');
  const [address, setAddress] = useState('');
  const [website, setWebsite] = useState('');
  const [phone, setPhone] = useState('');
  const [professionalEmail, setProfessionalEmail] = useState('');
  const [hourlyRate, setHourlyRate] = useState('');

  // Availability state
  const [availabilityStartDate, setAvailabilityStartDate] = useState('');
  const [availabilityEndDate, setAvailabilityEndDate] = useState('');
  const [availabilityHours, setAvailabilityHours] = useState('40');
  const [availabilityNotes, setAvailabilityNotes] = useState('');

  // CV Upload state
  const [isUploadingCV, setIsUploadingCV] = useState(false);
  const [cvFileName, setCvFileName] = useState<string | null>(null);

  // Update state when settings load
  useEffect(() => {
    if (settings) {
      setFullName(settings.full_name || '');
      setAddress(settings.address || '');
      setWebsite(settings.website || '');
      setPhone(settings.phone || '');
      setProfessionalEmail(settings.professional_email || '');
      setHourlyRate(settings.hourly_rate_eur?.toString() || '');
      setAvailabilityStartDate(settings.availability_start_date || '');
      setAvailabilityEndDate(settings.availability_end_date || '');
      setAvailabilityHours(settings.availability_hours_per_week?.toString() || '40');
      setAvailabilityNotes(settings.availability_notes || '');
      
      // Check if CV exists
      if (settings.cv_pdf_url) {
        const fileName = settings.cv_pdf_url.split('/').pop();
        setCvFileName(fileName || 'CV.pdf');
      } else {
        setCvFileName(null);
      }
    }
  }, [settings]);

  const handleCVUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      toast({
        title: 'Ungültiger Dateityp',
        description: 'Bitte wählen Sie eine PDF-Datei aus.',
        variant: 'destructive'
      });
      return;
    }

    setIsUploadingCV(true);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) {
        throw new Error('Nicht authentifiziert');
      }

      // Upload PDF to storage
      const fileName = `${session.user.id}/cv_${Date.now()}.pdf`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('cv-uploads')
        .upload(fileName, file, {
          contentType: 'application/pdf',
          upsert: true
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('cv-uploads')
        .getPublicUrl(fileName);

      // Save URL to user settings
      const { error: updateError } = await supabase
        .from('user_settings')
        .upsert({
          user_id: session.user.id,
          cv_pdf_url: urlData.publicUrl,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (updateError) throw updateError;

      setCvFileName(file.name);
      
      toast({
        title: 'CV erfolgreich hochgeladen',
        description: 'Ihr CV steht jetzt für Bewerbungen zur Verfügung.',
      });
      
    } catch (error) {
      console.error('CV upload error:', error);
      toast({
        title: 'Fehler beim CV-Upload',
        description: error.message || 'Ein unbekannter Fehler ist aufgetreten.',
        variant: 'destructive'
      });
    } finally {
      setIsUploadingCV(false);
    }
  };

  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await saveSettings({
        // Profile data
        full_name: fullName || undefined,
        address: address || undefined,
        website: website || undefined,
        phone: phone || undefined,
        professional_email: professionalEmail || undefined,
        hourly_rate_eur: hourlyRate ? parseInt(hourlyRate) : undefined,
        // Availability data
        availability_start_date: availabilityStartDate || undefined,
        availability_end_date: availabilityEndDate || undefined,
        availability_hours_per_week: availabilityHours ? parseInt(availabilityHours) : undefined,
        availability_notes: availabilityNotes || undefined
      });
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };



  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importData: ExportData = JSON.parse(e.target?.result as string);
        
        // Here you would implement the import logic
        // For now, just show a toast
        toast({
          title: 'Import bereit',
          description: `${importData.projects?.length || 0} Projekte gefunden. Import-Funktionalität wird implementiert.`,
        });
      } catch (error) {
        toast({
          title: 'Import-Fehler',
          description: 'Die Datei konnte nicht gelesen werden.',
          variant: 'destructive'
        });
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="space-y-4 sm:space-y-6 max-w-full overflow-x-hidden">


      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
            <User className="h-5 w-5" />
            Profil-Informationen
          </CardTitle>
          <CardDescription className="text-sm">
            Diese Daten werden in automatisch generierten Bewerbungen verwendet.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSaveSettings} className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="full-name">Vollständiger Name</Label>
                <Input
                  id="full-name"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  placeholder="Max Mustermann"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="professional-email">Professionelle E-Mail</Label>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <Input
                    id="professional-email"
                    type="email"
                    value={professionalEmail}
                    onChange={(e) => setProfessionalEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Telefonnummer</Label>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <Input
                    id="phone"
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="+49 123 456789"
                    className="min-w-0"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="website">Website/Portfolio</Label>
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <Input
                    id="website"
                    type="url"
                    value={website}
                    onChange={(e) => setWebsite(e.target.value)}
                    placeholder="https://www.example.com"
                    className="min-w-0"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="hourly-rate">Stundensatz (EUR, netto)</Label>
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground flex-shrink-0">€</span>
                  <Input
                    id="hourly-rate"
                    type="number"
                    min="1"
                    max="1000"
                    value={hourlyRate}
                    onChange={(e) => setHourlyRate(e.target.value)}
                    placeholder="85"
                    className="w-20 sm:w-32 min-w-0"
                  />
                  <span className="text-xs sm:text-sm text-muted-foreground">/Stunde (netto)</span>
                </div>
              </div>
              <div></div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Adresse</Label>
              <div className="flex items-start gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground mt-2" />
                <Textarea
                  id="address"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  placeholder="Musterstraße 123&#10;12345 Musterstadt&#10;Deutschland"
                  rows={3}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                <h3 className="text-lg font-semibold">Verfügbarkeit</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start-date">Verfügbar ab</Label>
                  <Input
                    id="start-date"
                    type="date"
                    value={availabilityStartDate}
                    onChange={(e) => setAvailabilityStartDate(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end-date">Verfügbar bis</Label>
                  <Input
                    id="end-date"
                    type="date"
                    value={availabilityEndDate}
                    onChange={(e) => setAvailabilityEndDate(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="hours">Stunden pro Woche</Label>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <Input
                    id="hours"
                    type="number"
                    min="1"
                    max="80"
                    value={availabilityHours}
                    onChange={(e) => setAvailabilityHours(e.target.value)}
                    className="w-32"
                  />
                  <span className="text-sm text-muted-foreground">Stunden/Woche</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Verfügbarkeits-Notizen</Label>
                <Textarea
                  id="notes"
                  placeholder="Zusätzliche Informationen zu Ihrer Verfügbarkeit..."
                  value={availabilityNotes}
                  onChange={(e) => setAvailabilityNotes(e.target.value)}
                  rows={3}
                />
              </div>
            </div>

            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                  Speichern...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Einstellungen speichern
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            CV/Lebenslauf
          </CardTitle>
          <CardDescription>
            Laden Sie Ihren CV hoch für personalisierte KI-Bewerbungen.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {cvFileName && (
            <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div className="flex flex-col">
                  <span className="text-green-700 dark:text-green-300 font-medium">CV hochgeladen</span>
                  <span className="text-sm text-green-600 dark:text-green-400">{cvFileName}</span>
                </div>
              </div>
              <div className="flex gap-2">
                <Button 
                  size="sm"
                  variant="outline"
                  onClick={async () => {
                    if (settings?.cv_pdf_url) {
                      try {
                        // Extract file path from the stored URL
                        const urlParts = settings.cv_pdf_url.split('/');
                        const fileName = urlParts[urlParts.length - 1];
                        const userId = urlParts[urlParts.length - 2];
                        const filePath = `${userId}/${fileName}`;
                        
                        // Generate signed URL for private bucket
                        const { data: signedUrlData, error: signedUrlError } = await supabase.storage
                          .from('cv-uploads')
                          .createSignedUrl(filePath, 60); // 60 seconds expiry
                        
                        if (signedUrlError) {
                          console.error('Error creating signed URL:', signedUrlError);
                          toast({
                            title: 'Fehler beim Laden',
                            description: 'Die CV-Datei konnte nicht geladen werden.',
                            variant: 'destructive'
                          });
                          return;
                        }
                        
                        window.open(signedUrlData.signedUrl, '_blank', 'noopener,noreferrer');
                      } catch (error) {
                        console.error('Error opening CV:', error);
                        toast({
                          title: 'Fehler beim Öffnen',
                          description: 'Die CV-Datei konnte nicht geöffnet werden.',
                          variant: 'destructive'
                        });
                      }
                    } else {
                      toast({
                        title: 'Keine CV-URL gefunden',
                        description: 'Bitte laden Sie Ihren CV erneut hoch.',
                        variant: 'destructive'
                      });
                    }
                  }}
                >
                  <FileText className="h-4 w-4 mr-1" />
                  Ansehen
                </Button>
              </div>
            </div>
          )}
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="cv-upload">
                {cvFileName ? 'CV ersetzen (PDF)' : 'CV hochladen (PDF)'}
              </Label>
              <Input
                id="cv-upload"
                type="file"
                accept=".pdf"
                onChange={handleCVUpload}
                disabled={isUploadingCV}
              />
              {isUploadingCV && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                  Hochladen...
                </div>
              )}
            </div>
            
            <div className="text-sm text-muted-foreground space-y-1">
              <p>• {cvFileName ? 'Laden Sie eine neue PDF hoch um die bestehende zu ersetzen' : 'Laden Sie Ihren CV als PDF hoch'}</p>
              <p>• Wird automatisch bei der Bewerbungsgenerierung verwendet</p>
              <p>• Diese Daten werden für personalisierte Bewerbungen verwendet</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Datenmanagement
          </CardTitle>
          <CardDescription>
            Exportieren und importieren Sie Ihre Projekte und Einstellungen in verschiedenen Formaten.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Export Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              <h4 className="font-medium">Export</h4>
            </div>

            {/* Progress indicator */}
            {progress && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>{progress.message}</span>
                  <span>{progress.progress}%</span>
                </div>
                <Progress value={progress.progress} className="h-2" />
              </div>
            )}

            {/* Export buttons */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              <Button
                onClick={() => exportToPDF({ includeSettings: true, includeNotes: true })}
                variant="outline"
                disabled={isExporting}
                className="flex items-center gap-2"
              >
                <FileType className="h-4 w-4" />
                PDF Export
              </Button>

              <Button
                onClick={() => exportToExcel({ includeSettings: true, includeNotes: true })}
                variant="outline"
                disabled={isExporting}
                className="flex items-center gap-2"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Excel Export
              </Button>

              <Button
                onClick={() => exportToJSON({ includeSettings: true })}
                variant="outline"
                disabled={isExporting}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                JSON Export
              </Button>
            </div>

            <div className="text-sm text-muted-foreground space-y-1">
              <p><strong>PDF:</strong> Formatierter Bericht mit allen Projektdetails</p>
              <p><strong>Excel:</strong> Strukturierte Tabelle für weitere Bearbeitung</p>
              <p><strong>JSON:</strong> Vollständige Daten für Backup und Import</p>
            </div>
          </div>

          <Separator />

          {/* Import Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              <h4 className="font-medium">Import</h4>
            </div>

            <div className="flex-1">
              <Label htmlFor="import-file" className="sr-only">
                Daten importieren
              </Label>
              <div className="relative">
                <Input
                  id="import-file"
                  type="file"
                  accept=".json"
                  onChange={handleImportData}
                  className="sr-only"
                />
                <Button
                  variant="outline"
                  className="w-full sm:w-auto"
                  onClick={() => document.getElementById('import-file')?.click()}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  JSON-Datei importieren
                </Button>
              </div>
            </div>

            <div className="text-sm text-muted-foreground">
              <p className="text-amber-600">⚠️ Beim Import werden bestehende Daten überschrieben.</p>
            </div>
          </div>
        </CardContent>
      </Card>

    </div>
  );
};