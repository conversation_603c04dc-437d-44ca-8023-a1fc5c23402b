import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { FreelanceProject, CreateProjectData, UpdateProjectData } from '@/types/freelance';
import { useToast } from '@/hooks/use-toast';
import { useEffect, useState } from 'react';

export const useFreelanceProjects = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setIsAuthenticated(!!session?.user);
    };

    checkAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setIsAuthenticated(!!session?.user);
    });

    return () => subscription.unsubscribe();
  }, []);

  const {
    data: projects = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['freelance-projects'],
    queryFn: async (): Promise<FreelanceProject[]> => {
      console.log('Starting project fetch...');

      // Get current user session with retry logic
      let session = null;
      let retries = 3;

      while (retries > 0 && !session) {
        const { data } = await supabase.auth.getSession();
        session = data.session;

        if (!session) {
          console.log(`No session found, retries left: ${retries - 1}`);
          retries--;
          if (retries > 0) {
            await new Promise(resolve => setTimeout(resolve, 100)); // Wait 100ms
          }
        }
      }

      if (!session?.user) {
        console.log('No authenticated user found after retries');
        return [];
      }

      console.log('Fetching projects for user:', session.user.id);

      // Query without user_id filter - let RLS handle it
      const { data, error } = await supabase
        .from('freelance_projects')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching projects:', error);
        // If it's a 403 error, it might be an RLS issue
        if (error.code === 'PGRST301' || error.message?.includes('403')) {
          console.error('RLS policy might be blocking access');
        }
        throw error;
      }

      console.log('Fetched projects:', data);
      return (data || []) as FreelanceProject[];
    },
    enabled: isAuthenticated, // Only run when authenticated
    retry: (failureCount, error) => {
      console.log(`Query failed (attempt ${failureCount + 1}):`, error);
      return failureCount < 2; // Retry up to 2 times
    },
    retryDelay: 1000 // Wait 1 second between retries
  });

  const createProject = useMutation({
    mutationFn: async (projectData: CreateProjectData) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      console.log('Creating project with data:', {
        ...projectData,
        user_id: user.id
      });

      const { data, error } = await supabase
        .from('freelance_projects')
        .insert([{
          ...projectData,
          user_id: user.id
        }])
        .select()
        .single();

      if (error) {
        console.error('Supabase error details:', error);
        throw error;
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['freelance-projects'] });
      toast({
        title: 'Projekt erstellt',
        description: 'Neues Projekt wurde erfolgreich hinzugefügt.'
      });
    },
    onError: (error) => {
      console.error('Error creating project:', error);

      // Detaillierte Fehlermeldung
      let errorMessage = 'Projekt konnte nicht erstellt werden.';
      if (error.message) {
        errorMessage += ` Fehler: ${error.message}`;
      }

      toast({
        title: 'Fehler',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  });

  const updateProject = useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateProjectData) => {
      const { data, error } = await supabase
        .from('freelance_projects')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['freelance-projects'] });
      toast({
        title: 'Projekt aktualisiert',
        description: 'Projekt wurde erfolgreich aktualisiert.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Projekt konnte nicht aktualisiert werden.',
        variant: 'destructive'
      });
      console.error('Error updating project:', error);
    }
  });

  const deleteProject = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('freelance_projects')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['freelance-projects'] });
      toast({
        title: 'Projekt gelöscht',
        description: 'Projekt wurde erfolgreich gelöscht.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Projekt konnte nicht gelöscht werden.',
        variant: 'destructive'
      });
      console.error('Error deleting project:', error);
    }
  });

  return {
    projects,
    isLoading,
    error,
    createProject: createProject.mutateAsync,
    updateProject: updateProject.mutateAsync,
    deleteProject: deleteProject.mutateAsync,
    isCreating: createProject.isPending,
    isUpdating: updateProject.isPending,
    isDeleting: deleteProject.isPending
  };
};