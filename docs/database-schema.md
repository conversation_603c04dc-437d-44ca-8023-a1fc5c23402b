# Freelancify Database Schema Documentation

## Overview
This document describes the simplified database schema for the Freelancify application, a user-based freelance project management system with direct user-to-project relationships.

## Architecture

### User-Based Model
- **Direct user ownership**: Each user owns their projects directly
- **Simple data isolation**: Users can only access their own data
- **No organization complexity**: Removed multi-tenant organization layer
- **Secure by default**: Row Level Security (RLS) enforces user-based access controls

## Tables

### 1. user_settings
**Purpose**: Stores user-specific preferences and profile data (not organization-specific).

**Columns**:
- `id` (UUID, PK): Unique identifier
- `user_id` (UUID, FK → auth.users, UNIQUE): User reference
- `full_name` (TEXT): User's full name
- `avatar_url` (TEXT): Profile picture URL
- `timezone` (TEXT): User's timezone (default: 'UTC')
- `language` (TEXT): Preferred language (default: 'de')
- `email_notifications` (BOOLEAN): Email notification preference (default: true)
- `created_at`, `updated_at` (TIMESTAMPTZ): Timestamps

### 2. freelance_projects
**Purpose**: Core business entity representing freelance projects with direct user ownership.

**Columns**:
- `id` (UUID, PK): Unique identifier
- `user_id` (UUID, FK → auth.users, NOT NULL): Project owner
- `project_name` (TEXT, NOT NULL): Project name
- `company_name` (TEXT, NOT NULL): Company name
- `contact_person` (TEXT): Contact person name
- `contact_email` (TEXT): Contact email address
- `contact_phone` (TEXT): Contact phone number
- `project_description` (TEXT): Project description
- `budget_range` (TEXT): Budget range
- `project_start_date` (DATE): Project start date
- `project_end_date` (DATE): Project end date
- `required_skills` (TEXT[]): Required skills array
- `application_date` (DATE): Application date
- `status` (TEXT): Application status
- `application_text` (TEXT): Application text
- `notes` (TEXT): Additional notes
- `source` (TEXT): Project source
- `listing_url` (TEXT): Project listing URL
- `work_location_type` (TEXT): Work location type
- `remote_percentage` (INTEGER): Remote work percentage
- `work_location_notes` (TEXT): Work location notes
- `created_at`, `updated_at` (TIMESTAMPTZ): Timestamps

**Constraints**:
- Project name and company name must not be empty
- Project end date must be after start date (if both set)
- Contact email must be valid format (if provided)
- Status must be valid enum value
- Remote percentage must be between 0-100 (if provided)

## Indexes

### Performance Indexes
- **user_settings**: user_id
- **freelance_projects**: user_id, status, created_at, deadline

### Composite Indexes
- **freelance_projects**: (user_id, status)

## Security (Row Level Security)

### user_settings
- **ALL**: Users can only access their own settings

### freelance_projects
- **ALL**: Users can only access their own projects

## Functions

### public.update_updated_at_column()
**Purpose**: Trigger function to automatically update updated_at timestamps.
**Usage**: Applied to all tables with updated_at columns.

## Triggers

### Automatic Timestamp Updates
All tables with `updated_at` columns have triggers that automatically update the timestamp on row modifications:
- `update_user_settings_updated_at`
- `update_freelance_projects_updated_at`

## Usage Patterns

### User Registration
1. User registers → creates auth.users record
2. User can immediately start creating projects
3. No organization setup required

### Project Management
1. User creates projects directly
2. All projects belong to the authenticated user
3. RLS policies ensure data isolation
4. Simple, straightforward ownership model

## Migration and Deployment

### Fresh Database Setup
```sql
-- Apply the simplified schema
psql -d your_database -f supabase/migrations/002_user_based_schema.sql
```

### Migrating from Organization-Based Schema
```sql
-- 1. Clean up existing organization data
psql -d your_database -f database_cleanup_commands.sql

-- 2. Apply the new user-based schema
psql -d your_database -f supabase/migrations/002_user_based_schema.sql
```

### Verification Queries
```sql
-- Check tables exist
SELECT tablename FROM pg_tables WHERE schemaname = 'public';

-- Check RLS is enabled
SELECT tablename, rowsecurity FROM pg_tables
WHERE schemaname = 'public' AND rowsecurity = true;

-- Check policies exist
SELECT tablename, policyname FROM pg_policies
WHERE schemaname = 'public';
```

## Security Considerations

### Data Isolation
- Complete separation between users
- No cross-user data leakage possible
- RLS policies enforce access at database level

### Authentication
- All operations require authenticated user
- User context automatically applied to all queries
- No manual user ID passing required

### Authorization
- Simple user-based access control
- Users can only access their own data
- No complex role management needed

### Performance
- Optimized indexes for user-based queries
- Efficient RLS policy implementation
- Minimal overhead for security checks

This simplified schema provides a clean, secure, and maintainable foundation for the Freelancify user-based application.
