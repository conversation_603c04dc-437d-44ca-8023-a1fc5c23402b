# Freelancify Database Schema Documentation

## Overview
This document describes the simplified database schema for the Freelancify application, a user-based freelance project management system with direct user-to-project relationships.

## Architecture

### User-Based Model
- **Direct user ownership**: Each user owns their projects directly
- **Simple data isolation**: Users can only access their own data
- **No organization complexity**: Removed multi-tenant organization layer
- **Secure by default**: Row Level Security (RLS) enforces user-based access controls

## Tables

### 1. user_settings
**Purpose**: Comprehensive user profile and preferences for application generation and profile management.

**Columns**:
- `id` (UUID, PK): Unique identifier
- `user_id` (UUID, FK → auth.users, UNIQUE): User reference
- `full_name` (TEXT): User's full name
- `professional_email` (TEXT): Professional email address
- `phone` (TEXT): Phone number
- `address` (TEXT): Physical address
- `website` (TEXT): Personal/professional website
- `hourly_rate_eur` (INTEGER): Hourly rate in EUR (1-10000)
- `availability_start_date` (DATE): Availability start date
- `availability_end_date` (DATE): Availability end date
- `availability_hours_per_week` (INTEGER): Available hours per week (1-168)
- `availability_notes` (TEXT): Additional availability information
- `cv_pdf_url` (TEXT): URL to uploaded CV PDF
- `created_at`, `updated_at` (TIMESTAMPTZ): Timestamps

**Constraints**:
- Each user can have only one settings record
- Valid email format validation for professional_email
- Valid website URL format validation
- Availability end date must be after start date (if both provided)
- Hourly rate must be between 1-10000 EUR (if provided)
- Hours per week must be between 1-168 (if provided)

### 2. freelance_projects
**Purpose**: Core business entity representing freelance projects with direct user ownership.

**Columns**:
- `id` (UUID, PK): Unique identifier
- `user_id` (UUID, FK → auth.users, NOT NULL): Project owner
- `project_name` (TEXT, NOT NULL): Project name
- `company_name` (TEXT, NOT NULL): Company name
- `contact_person` (TEXT): Contact person name
- `contact_email` (TEXT): Contact email address
- `contact_phone` (TEXT): Contact phone number
- `project_description` (TEXT): Project description
- `budget_range` (TEXT): Budget range
- `project_start_date` (DATE): Project start date
- `project_end_date` (DATE): Project end date
- `required_skills` (TEXT[]): Required skills array
- `application_date` (DATE): Application date
- `status` (TEXT): Application status
- `application_text` (TEXT): Application text
- `notes` (TEXT): Additional notes
- `source` (TEXT): Project source
- `listing_url` (TEXT): Project listing URL
- `work_location_type` (TEXT): Work location type
- `remote_percentage` (INTEGER): Remote work percentage
- `work_location_notes` (TEXT): Work location notes
- `created_at`, `updated_at` (TIMESTAMPTZ): Timestamps

**Constraints**:
- Project name and company name must not be empty
- Project end date must be after start date (if both set)
- Contact email must be valid format (if provided)
- Status must be valid enum value
- Remote percentage must be between 0-100 (if provided)

## Indexes

### Performance Indexes
- **user_settings**: user_id
- **freelance_projects**: user_id, status, created_at, deadline

### Composite Indexes
- **freelance_projects**: (user_id, status)

## Security (Row Level Security)

### user_settings
- **ALL**: Users can only access their own settings

### freelance_projects
- **ALL**: Users can only access their own projects

## Functions

### public.update_updated_at_column()
**Purpose**: Trigger function to automatically update updated_at timestamps.
**Usage**: Applied to all tables with updated_at columns.

## Triggers

### Automatic Timestamp Updates
All tables with `updated_at` columns have triggers that automatically update the timestamp on row modifications:
- `update_user_settings_updated_at`
- `update_freelance_projects_updated_at`

## Usage Patterns

### User Registration
1. User registers → creates auth.users record
2. User can immediately start creating projects
3. No organization setup required

### Project Management
1. User creates projects directly
2. All projects belong to the authenticated user
3. RLS policies ensure data isolation
4. Simple, straightforward ownership model

## Migration and Deployment

### Fresh Database Setup
```sql
-- Apply the comprehensive schema to an empty database
psql -d your_database -f supabase/migrations/001_comprehensive_schema.sql
```

### Migrating from Existing Schema
```sql
-- 1. Complete cleanup of existing data and tables
psql -d your_database -f database_complete_cleanup.sql

-- 2. Apply the new comprehensive schema
psql -d your_database -f supabase/migrations/001_comprehensive_schema.sql
```

## Storage

### CV Upload Bucket
- **Bucket ID**: `cv-uploads`
- **File Size Limit**: 5MB
- **Allowed Types**: PDF only
- **Access Control**: Users can only access their own files
- **Folder Structure**: Files organized by user ID

### Verification Queries
```sql
-- Check tables exist
SELECT tablename FROM pg_tables WHERE schemaname = 'public';

-- Check RLS is enabled
SELECT tablename, rowsecurity FROM pg_tables
WHERE schemaname = 'public' AND rowsecurity = true;

-- Check policies exist
SELECT tablename, policyname FROM pg_policies
WHERE schemaname = 'public';
```

## Security Considerations

### Data Isolation
- Complete separation between users
- No cross-user data leakage possible
- RLS policies enforce access at database level

### Authentication
- All operations require authenticated user
- User context automatically applied to all queries
- No manual user ID passing required

### Authorization
- Simple user-based access control
- Users can only access their own data
- No complex role management needed

### Performance
- Optimized indexes for user-based queries
- Efficient RLS policy implementation
- Minimal overhead for security checks

This simplified schema provides a clean, secure, and maintainable foundation for the Freelancify user-based application.
